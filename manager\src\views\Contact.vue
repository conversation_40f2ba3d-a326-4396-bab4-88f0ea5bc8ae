<template>
  <div class="contact">
    <el-card>
      <template #header>
        <h2>联系我们</h2>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="contact-info">
            <template #header>
              <h3>联系信息</h3>
            </template>
            
            <el-descriptions :column="1" border>
              <el-descriptions-item label="公司名称">
                北京会议管理有限公司
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                <el-link type="primary">010-12345678</el-link>
              </el-descriptions-item>
              <el-descriptions-item label="客服热线">
                <el-link type="primary">400-123-4567</el-link>
              </el-descriptions-item>
              <el-descriptions-item label="邮箱地址">
                <el-link type="primary"><EMAIL></el-link>
              </el-descriptions-item>
              <el-descriptions-item label="公司地址">
                北京市朝阳区建国路88号现代城A座15层
              </el-descriptions-item>
              <el-descriptions-item label="工作时间">
                周一至周五 9:00-18:00
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="contact-form">
            <template #header>
              <h3>在线留言</h3>
            </template>
            
            <el-form
              ref="contactFormRef"
              :model="contactForm"
              :rules="rules"
              label-width="80px"
            >
              <el-form-item label="姓名" prop="name">
                <el-input v-model="contactForm.name" placeholder="请输入您的姓名" />
              </el-form-item>
              
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="contactForm.email" placeholder="请输入您的邮箱" />
              </el-form-item>
              
              <el-form-item label="电话" prop="phone">
                <el-input v-model="contactForm.phone" placeholder="请输入您的电话" />
              </el-form-item>
              
              <el-form-item label="主题" prop="subject">
                <el-input v-model="contactForm.subject" placeholder="请输入留言主题" />
              </el-form-item>
              
              <el-form-item label="内容" prop="message">
                <el-input
                  v-model="contactForm.message"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入留言内容"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="submitForm">
                  提交留言
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const contactFormRef = ref()
const loading = ref(false)

const contactForm = reactive({
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: ''
})

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入主题', trigger: 'blur' }
  ],
  message: [
    { required: true, message: '请输入留言内容', trigger: 'blur' }
  ]
}

const submitForm = async () => {
  if (!contactFormRef.value) return
  
  await contactFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      // 模拟提交
      setTimeout(() => {
        loading.value = false
        ElMessage.success('留言提交成功，我们会尽快回复您！')
        resetForm()
      }, 1000)
    }
  })
}

const resetForm = () => {
  contactFormRef.value?.resetFields()
}
</script>

<style scoped>
.contact {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.contact-info,
.contact-form {
  height: 100%;
}
</style>
