<template>
  <div class="payment">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>支付方式管理</h2>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加支付方式
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8" v-for="payment in paymentMethods" :key="payment.id">
          <el-card class="payment-card" :class="{ 'default-payment': payment.isDefault }">
            <div class="payment-info">
              <div class="payment-icon">
                <el-icon size="24" v-if="payment.type === 'alipay'"><Money /></el-icon>
                <el-icon size="24" v-else-if="payment.type === 'wechat'"><ChatDotRound /></el-icon>
                <el-icon size="24" v-else><CreditCard /></el-icon>
              </div>
              <div class="payment-details">
                <h4>{{ payment.name }}</h4>
                <p>{{ payment.account }}</p>
                <el-tag v-if="payment.isDefault" type="success" size="small">默认</el-tag>
              </div>
            </div>
            <div class="payment-actions">
              <el-button size="small" @click="setDefault(payment.id)" :disabled="payment.isDefault">
                设为默认
              </el-button>
              <el-button size="small" type="danger" @click="deletePayment(payment.id)">
                删除
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 支付记录 -->
      <el-card style="margin-top: 20px;">
        <template #header>
          <h3>支付记录</h3>
        </template>
        
        <el-table :data="paymentHistory" style="width: 100%">
          <el-table-column prop="date" label="支付时间" width="180" />
          <el-table-column prop="amount" label="支付金额" width="120">
            <template #default="scope">
              <span class="amount">¥{{ scope.row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="method" label="支付方式" width="120" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>
    
    <!-- 添加支付方式对话框 -->
    <el-dialog v-model="showAddDialog" title="添加支付方式" width="500px">
      <el-form
        ref="paymentFormRef"
        :model="paymentForm"
        :rules="paymentRules"
        label-width="100px"
      >
        <el-form-item label="支付方式" prop="type">
          <el-select v-model="paymentForm.type" placeholder="请选择支付方式">
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信支付" value="wechat" />
            <el-option label="银行卡" value="bank" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="账户名称" prop="name">
          <el-input v-model="paymentForm.name" placeholder="请输入账户名称" />
        </el-form-item>
        
        <el-form-item label="账户信息" prop="account">
          <el-input v-model="paymentForm.account" placeholder="请输入账户信息" />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="paymentForm.isDefault">设为默认支付方式</el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="addPaymentMethod">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Money, ChatDotRound, CreditCard } from '@element-plus/icons-vue'

const showAddDialog = ref(false)
const addLoading = ref(false)
const paymentFormRef = ref()

const paymentForm = reactive({
  type: '',
  name: '',
  account: '',
  isDefault: false
})

const paymentRules = {
  type: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入账户名称', trigger: 'blur' }
  ],
  account: [
    { required: true, message: '请输入账户信息', trigger: 'blur' }
  ]
}

const paymentMethods = ref([
  {
    id: 1,
    type: 'alipay',
    name: '支付宝',
    account: '138****8888',
    isDefault: true
  },
  {
    id: 2,
    type: 'wechat',
    name: '微信支付',
    account: 'wx_****1234',
    isDefault: false
  }
])

const paymentHistory = ref([
  {
    date: '2024-01-15 14:30:00',
    amount: '2000.00',
    method: '支付宝',
    description: '会议注册费用',
    status: '成功'
  },
  {
    date: '2024-01-10 09:15:00',
    amount: '1500.00',
    method: '微信支付',
    description: '培训费用',
    status: '成功'
  }
])

const addPaymentMethod = async () => {
  if (!paymentFormRef.value) return
  
  await paymentFormRef.value.validate(async (valid) => {
    if (valid) {
      addLoading.value = true
      // 模拟添加
      setTimeout(() => {
        const newPayment = {
          id: Date.now(),
          ...paymentForm
        }
        
        if (paymentForm.isDefault) {
          paymentMethods.value.forEach(p => p.isDefault = false)
        }
        
        paymentMethods.value.push(newPayment)
        addLoading.value = false
        showAddDialog.value = false
        ElMessage.success('支付方式添加成功！')
        
        // 重置表单
        paymentFormRef.value.resetFields()
        Object.assign(paymentForm, {
          type: '',
          name: '',
          account: '',
          isDefault: false
        })
      }, 1000)
    }
  })
}

const setDefault = (id) => {
  paymentMethods.value.forEach(p => {
    p.isDefault = p.id === id
  })
  ElMessage.success('默认支付方式设置成功！')
}

const deletePayment = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个支付方式吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = paymentMethods.value.findIndex(p => p.id === id)
    if (index > -1) {
      paymentMethods.value.splice(index, 1)
      ElMessage.success('删除成功！')
    }
  } catch {
    // 用户取消删除
  }
}
</script>

<style scoped>
.payment {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
}

.payment-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.payment-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.default-payment {
  border: 2px solid #67c23a;
}

.payment-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.payment-icon {
  margin-right: 15px;
  color: #409eff;
}

.payment-details h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.payment-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.payment-actions {
  display: flex;
  gap: 10px;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
}
</style>
