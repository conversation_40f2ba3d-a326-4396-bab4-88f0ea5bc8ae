import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  
  const isLoggedIn = computed(() => !!token.value)
  
  const login = async (credentials) => {
    try {
      const response = await mockLogin(credentials)
      user.value = response.user
      token.value = response.token
      localStorage.setItem('token', response.token)
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const logout = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('token')
  }
  
  return {
    user,
    token,
    isLoggedIn,
    login,
    logout
  }
})

// 模拟登录 API
const mockLogin = (credentials) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (credentials.username === 'admin' && credentials.password === '123456') {
        resolve({
          user: {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            realName: '管理员',
            phone: '',
            wechat: '',
            avatar: ''
          },
          token: 'mock-jwt-token'
        })
      } else {
        reject(new Error('用户名或密码错误'))
      }
    }, 1000)
  })
}
