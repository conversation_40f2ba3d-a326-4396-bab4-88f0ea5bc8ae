<template>
  <div class="profile">
    <el-card>
      <template #header>
        <h2>个人信息</h2>
      </template>
      
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="rules"
        label-width="100px"
        style="max-width: 600px"
      >
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="handleAvatarUpload"
          >
            <el-avatar v-if="profileForm.avatar" :size="80" :src="profileForm.avatar" />
            <el-icon v-else class="avatar-uploader-icon" :size="80"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="profileForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        
        <el-form-item label="微信">
          <div class="wechat-bind">
            <el-input 
              v-model="profileForm.wechat" 
              placeholder="未绑定微信" 
              disabled 
              style="flex: 1"
            />
            <el-button 
              type="primary" 
              :loading="wechatLoading"
              @click="handleBindWechat"
              style="margin-left: 10px"
            >
              {{ profileForm.wechat ? '重新绑定' : '绑定微信' }}
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            保存修改
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const profileFormRef = ref()
const loading = ref(false)
const wechatLoading = ref(false)

const profileForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  wechat: '',
  avatar: ''
})

const rules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const initForm = () => {
  if (userStore.user) {
    Object.assign(profileForm, userStore.user)
  }
}

const resetForm = () => {
  initForm()
}

const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const handleAvatarUpload = (options) => {
  // 模拟上传
  const reader = new FileReader()
  reader.onload = (e) => {
    profileForm.avatar = e.target.result
  }
  reader.readAsDataURL(options.file)
}

const handleBindWechat = async () => {
  wechatLoading.value = true
  const result = await userStore.bindWechat()
  wechatLoading.value = false
  
  if (result.success) {
    profileForm.wechat = userStore.user.wechat
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

const handleSubmit = async () => {
  if (!profileFormRef.value) return
  
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const result = await userStore.updateProfile(profileForm)
      loading.value = false
      
      if (result.success) {
        ElMessage.success(result.message)
      } else {
        ElMessage.error(result.message)
      }
    }
  })
}

onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile {
  padding: 20px;
}

.avatar-uploader {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  color: #8c939d;
}

.wechat-bind {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
