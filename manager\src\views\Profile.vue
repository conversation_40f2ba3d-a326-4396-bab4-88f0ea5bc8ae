<template>
  <div class="profile-page">
    <!-- 顶部个人信息卡片 -->
    <div class="profile-header">
      <el-card class="profile-card">
        <div class="user-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="profileForm.avatar">
              <el-icon size="40"><User /></el-icon>
            </el-avatar>
          </div>
          <div class="user-details">
            <h2 class="username">{{ profileForm.username || '王国' }}</h2>
            <div class="user-tags">
              <el-tag type="success" class="member-tag">省市级会员</el-tag>
              <el-tag type="warning" class="member-tag">市级会员</el-tag>
              <el-tag type="info" class="member-tag">市级会员</el-tag>
            </div>
            <p class="user-desc">
              欢迎来到会议管理系统，您可以在这里管理您的会议信息和个人资料。
            </p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 注册费用表格 -->
    <div class="registration-fees">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>注册费</h3>
          </div>
        </template>

        <el-table :data="feeData" style="width: 100%" border>
          <el-table-column prop="memberType" label="会员人员身份" align="center" />
          <el-table-column prop="beforeDate" label="7月15日前" align="center">
            <template #default="scope">
              <span class="fee-amount">{{ scope.row.beforeDate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="afterDate" label="7月15日后" align="center">
            <template #default="scope">
              <span class="fee-amount">{{ scope.row.afterDate }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 注册提醒 -->
    <div class="registration-notice">
      <el-alert
        title="注册提醒"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>请注意注册截止时间，提前注册可享受优惠价格。如有疑问，请联系会议组织方。</p>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'

const userStore = useUserStore()

const profileForm = reactive({
  username: '王国',
  realName: '王国',
  email: '<EMAIL>',
  phone: '138****8888',
  wechat: '',
  avatar: ''
})

// 注册费用数据
const feeData = ref([
  {
    memberType: '普通注册者(含博士后)',
    beforeDate: '2000元/人',
    afterDate: '2200元/人'
  },
  {
    memberType: '在校学生',
    beforeDate: '1500元/人',
    afterDate: '1600元/人'
  }
])

const initForm = () => {
  if (userStore.user) {
    Object.assign(profileForm, userStore.user)
  }
}

onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  height: 100%;
}

.profile-header {
  margin-bottom: 20px;
}

.profile-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.avatar-section {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.username {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.user-tags {
  margin-bottom: 15px;
}

.member-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.user-desc {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.registration-fees {
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: bold;
}

.fee-amount {
  font-weight: bold;
  color: #E6A23C;
}

.registration-notice {
  margin-bottom: 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: bold;
}

:deep(.el-alert) {
  border-radius: 8px;
}
</style>
