<template>
  <div class="dashboard">
    <!-- 个人信息头部 -->
    <div class="profile-header">
      <el-card>
        <div class="profile-info">
          <div class="avatar-section">
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :before-upload="beforeAvatarUpload"
              :http-request="handleAvatarUpload"
            >
              <el-avatar :size="80" :src="userStore.user?.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
            </el-upload>
          </div>
          <div class="user-details">
            <h2>{{ userStore.user?.username || '用户' }}</h2>
            <p>{{ userStore.user?.email || '未设置邮箱' }}</p>
            <div class="user-actions">
              <el-button type="primary" @click="showProfileDialog = true">
                <el-icon><Edit /></el-icon>
                编辑资料
              </el-button>
              <el-button @click="showPasswordDialog = true">
                <el-icon><Lock /></el-icon>
                修改密码
              </el-button>
              <el-button @click="showPhoneDialog = true">
                <el-icon><Phone /></el-icon>
                绑定手机
              </el-button>
              <el-button @click="handleBindWechat" :loading="wechatLoading">
                <el-icon><ChatDotRound /></el-icon>
                绑定微信
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <h1>仪表板</h1>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card>
          <div class="stat-card">
            <el-icon size="40" color="#409EFF"><User /></el-icon>
            <div>
              <h3>1,234</h3>
              <p>用户总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card>
          <div class="stat-card">
            <el-icon size="40" color="#67C23A"><Document /></el-icon>
            <div>
              <h3>567</h3>
              <p>文章数量</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card>
          <div class="stat-card">
            <el-icon size="40" color="#E6A23C"><View /></el-icon>
            <div>
              <h3>8,901</h3>
              <p>访问量</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card>
          <div class="stat-card">
            <el-icon size="40" color="#F56C6C"><Money /></el-icon>
            <div>
              <h3>¥12,345</h3>
              <p>收入</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 个人信息编辑对话框 -->
    <el-dialog v-model="showProfileDialog" title="编辑个人信息" width="500px">
      <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="80px">
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showProfileDialog = false">取消</el-button>
        <el-button type="primary" :loading="profileLoading" @click="handleUpdateProfile">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="400px">
      <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="80px">
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
          修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 绑定手机号对话框 -->
    <el-dialog v-model="showPhoneDialog" title="绑定手机号" width="400px">
      <el-form ref="phoneFormRef" :model="phoneForm" :rules="phoneRules" label-width="80px">
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="phoneForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <div style="display: flex; gap: 10px;">
            <el-input v-model="phoneForm.code" placeholder="请输入验证码" />
            <el-button :disabled="codeDisabled" @click="sendCode">
              {{ codeText }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPhoneDialog = false">取消</el-button>
        <el-button type="primary" :loading="phoneLoading" @click="handleBindPhone">
          绑定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

// 对话框显示状态
const showProfileDialog = ref(false)
const showPasswordDialog = ref(false)
const showPhoneDialog = ref(false)

// 加载状态
const profileLoading = ref(false)
const passwordLoading = ref(false)
const phoneLoading = ref(false)
const wechatLoading = ref(false)

// 验证码相关
const codeDisabled = ref(false)
const codeText = ref('发送验证码')

// 表单引用
const profileFormRef = ref()
const passwordFormRef = ref()
const phoneFormRef = ref()

// 个人信息表单
const profileForm = reactive({
  realName: '',
  email: '',
  phone: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 手机号表单
const phoneForm = reactive({
  phone: '',
  code: ''
})

// 表单验证规则
const profileRules = {
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const phoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

// 头像上传
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const handleAvatarUpload = (options) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    // 模拟更新头像
    userStore.user.avatar = e.target.result
    ElMessage.success('头像更新成功')
  }
  reader.readAsDataURL(options.file)
}

// 更新个人信息
const handleUpdateProfile = async () => {
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      profileLoading.value = true
      // 模拟API调用
      setTimeout(() => {
        Object.assign(userStore.user, profileForm)
        profileLoading.value = false
        showProfileDialog.value = false
        ElMessage.success('个人信息更新成功')
      }, 1000)
    }
  })
}

// 修改密码
const handleChangePassword = async () => {
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordLoading.value = true
      // 模拟API调用
      setTimeout(() => {
        passwordLoading.value = false
        showPasswordDialog.value = false
        passwordForm.oldPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
        ElMessage.success('密码修改成功')
      }, 1000)
    }
  })
}

// 发送验证码
const sendCode = () => {
  if (!phoneForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(phoneForm.phone)) {
    ElMessage.error('请输入正确的手机号')
    return
  }

  codeDisabled.value = true
  let countdown = 60

  const timer = setInterval(() => {
    countdown--
    codeText.value = `${countdown}s后重发`

    if (countdown <= 0) {
      clearInterval(timer)
      codeDisabled.value = false
      codeText.value = '发送验证码'
    }
  }, 1000)

  ElMessage.success('验证码已发送')
}

// 绑定手机号
const handleBindPhone = async () => {
  await phoneFormRef.value.validate(async (valid) => {
    if (valid) {
      phoneLoading.value = true
      // 模拟API调用
      setTimeout(() => {
        userStore.user.phone = phoneForm.phone
        phoneLoading.value = false
        showPhoneDialog.value = false
        phoneForm.phone = ''
        phoneForm.code = ''
        ElMessage.success('手机号绑定成功')
      }, 1000)
    }
  })
}

// 绑定微信
const handleBindWechat = async () => {
  wechatLoading.value = true
  // 模拟API调用
  setTimeout(() => {
    userStore.user.wechat = 'wx_' + Math.random().toString(36).substr(2, 9)
    wechatLoading.value = false
    ElMessage.success('微信绑定成功')
  }, 1000)
}

// 初始化表单数据
const initForms = () => {
  if (userStore.user) {
    profileForm.realName = userStore.user.realName || ''
    profileForm.email = userStore.user.email || ''
    profileForm.phone = userStore.user.phone || ''
  }
}

onMounted(() => {
  initForms()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.profile-header {
  margin-bottom: 30px;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-uploader {
  cursor: pointer;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.3s;
}

.avatar-uploader:hover {
  transform: scale(1.05);
}

.user-details h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.user-details p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}

.user-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-card h3 {
  margin: 0;
  font-size: 24px;
}

.stat-card p {
  margin: 5px 0 0 0;
  color: #666;
}
</style>