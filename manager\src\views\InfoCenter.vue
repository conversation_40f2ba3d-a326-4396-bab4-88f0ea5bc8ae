<template>
  <div class="info-center">
    <el-card>
      <template #header>
        <h2>信息中心概况</h2>
      </template>
      
      <div class="info-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="info-card">
              <template #header>
                <h3>会议统计</h3>
              </template>
              <el-statistic title="总会议数" :value="totalMeetings" />
              <el-statistic title="本月会议" :value="monthlyMeetings" />
              <el-statistic title="参与人数" :value="totalParticipants" />
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card class="info-card">
              <template #header>
                <h3>系统信息</h3>
              </template>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="系统版本">v2.0.1</el-descriptions-item>
                <el-descriptions-item label="最后更新">2024-01-15</el-descriptions-item>
                <el-descriptions-item label="在线用户">{{ onlineUsers }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
        
        <el-card class="recent-activities" style="margin-top: 20px;">
          <template #header>
            <h3>最近活动</h3>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.timestamp"
              :type="activity.type"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const totalMeetings = ref(156)
const monthlyMeetings = ref(23)
const totalParticipants = ref(1248)
const onlineUsers = ref(45)

const recentActivities = ref([
  {
    id: 1,
    content: '新用户注册：张三',
    timestamp: '2024-01-15 10:30',
    type: 'primary'
  },
  {
    id: 2,
    content: '会议创建：技术分享会',
    timestamp: '2024-01-15 09:15',
    type: 'success'
  },
  {
    id: 3,
    content: '系统维护完成',
    timestamp: '2024-01-14 18:00',
    type: 'warning'
  }
])
</script>

<style scoped>
.info-center {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.info-card {
  height: 100%;
}

.recent-activities {
  margin-top: 20px;
}
</style>
